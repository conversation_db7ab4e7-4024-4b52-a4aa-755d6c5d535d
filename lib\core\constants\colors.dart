import 'package:flutter/material.dart';

class AppColors {
  // Modern Minimalist Theme - Primary Colors
  static const Color primaryBlue =
      Color(0xFF2563EB); // Professional blue accent
  static const Color primaryBlueDark =
      Color(0xFF1D4ED8); // Darker blue for pressed states
  static const Color primaryBlueLight =
      Color(0xFF3B82F6); // Lighter blue for hover states

  // Modern Minimalist - Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color gray50 = Color(0xFFF9FAFB);
  static const Color gray100 = Color(0xFFF3F4F6);
  static const Color gray200 = Color(0xFFE5E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);

  // Gradient Colors - Minimal and Clean
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [white, gray50],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [gray50, gray100],
  );

  static const LinearGradient blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryBlueDark],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFFFFF),
      Color(0xFFF8F9FA),
    ],
  );

  // Background Colors - Modern Minimalist
  static const Color backgroundColor = white;
  static const Color backgroundDark = gray900;
  static const Color surfaceColor = white;
  static const Color cardColor = white;
  static const Color cardDark = gray800;

  // Text Colors - Modern Minimalist
  static const Color textPrimary = gray900;
  static const Color textSecondary = gray600;
  static const Color textLight = gray400;
  static const Color textWhite = white;

  // Action Colors - Clean with blue accent
  static const Color likeColor = Color(0xFF10B981); // Green
  static const Color passColor = Color(0xFFEF4444); // Red
  static const Color superLikeColor = primaryBlue; // Blue accent
  static const Color boostColor = Color(0xFFF59E0B); // Amber

  // Status Colors - Modern
  static const Color successColor = Color(0xFF10B981);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color infoColor = primaryBlue;

  // Border Colors - Subtle grays
  static const Color borderColor = gray200;
  static const Color dividerColor = gray100;

  // Shadow Colors - Minimal
  static const Color shadowColor = Color(0x0A000000);
  static const Color cardShadowColor = Color(0x05000000);

  // Online Status
  static const Color onlineColor = Color(0xFF10B981);
  static const Color offlineColor = gray400;

  // Message Colors - Clean
  static const Color sentMessageColor = primaryBlue;
  static const Color receivedMessageColor = gray100;

  // Coin Colors
  static const Color coinGold = Color(0xFFFFD700);
  static const Color coinSilver = Color(0xFFC0C0C0);

  // Shimmer Colors - Subtle
  static const Color shimmerBase = gray100;
  static const Color shimmerHighlight = gray50;
}

class AppGradients {
  // Modern Minimalist Gradients
  static const List<Color> primary = [
    AppColors.white,
    AppColors.gray50,
  ];

  static const List<Color> secondary = [
    AppColors.gray50,
    AppColors.gray100,
  ];

  static const List<Color> blue = [
    AppColors.primaryBlue,
    AppColors.primaryBlueDark,
  ];

  static const List<Color> subtle = [
    AppColors.gray50,
    AppColors.gray100,
    AppColors.gray200,
  ];

  static const List<Color> card = [
    AppColors.white,
    AppColors.gray50,
  ];

  static const List<Color> surface = [
    AppColors.white,
    AppColors.white,
  ];
}
