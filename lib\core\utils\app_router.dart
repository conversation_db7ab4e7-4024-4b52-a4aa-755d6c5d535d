import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/home/<USER>/pages/main_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/matching/presentation/pages/modern_match_page.dart';
import '../../features/messaging/presentation/pages/chat_list_page.dart';
import '../../features/messaging/presentation/pages/chat_page.dart';
import '../../features/profile/presentation/pages/modern_profile_page.dart';
import '../../features/profile/presentation/pages/edit_profile_page.dart';
import '../../features/recharge/presentation/pages/recharge_page.dart';
import '../../features/settings/presentation/pages/notification_settings_page.dart';
import '../../features/settings/presentation/pages/privacy_settings_page.dart';
import '../../features/settings/presentation/pages/help_support_page.dart';
import '../services/storage_service.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    routes: [
      // Splash Route
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      // Main App Routes
      ShellRoute(
        builder: (context, state, child) => MainPage(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/matches',
            name: 'matches',
            builder: (context, state) => const ModernMatchPage(),
          ),
          GoRoute(
            path: '/messages',
            name: 'messages',
            builder: (context, state) => const ChatListPage(),
          ),
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ModernProfilePage(),
          ),
        ],
      ),

      // Chat Route
      GoRoute(
        path: '/chat/:conversationId',
        name: 'chat',
        builder: (context, state) {
          final conversationId = state.pathParameters['conversationId']!;
          final userName = state.uri.queryParameters['userName'] ?? 'User';
          final userAvatar = state.uri.queryParameters['userAvatar'];
          return ChatPage(
            conversationId: conversationId,
            userName: userName,
            userAvatar: userAvatar,
          );
        },
      ),

      // Edit Profile Route
      GoRoute(
        path: '/edit-profile',
        name: 'edit-profile',
        builder: (context, state) => const EditProfilePage(),
      ),

      // Recharge Route
      GoRoute(
        path: '/recharge',
        name: 'recharge',
        builder: (context, state) => const RechargePage(),
      ),

      // Settings Routes
      GoRoute(
        path: '/notification-settings',
        name: 'notification-settings',
        builder: (context, state) => const NotificationSettingsPage(),
      ),
      GoRoute(
        path: '/privacy-settings',
        name: 'privacy-settings',
        builder: (context, state) => const PrivacySettingsPage(),
      ),
      GoRoute(
        path: '/help-support',
        name: 'help-support',
        builder: (context, state) => const HelpSupportPage(),
      ),
    ],
    redirect: (context, state) async {
      final isLoggedIn = await StorageService.hasToken();
      final isOnboardingCompleted = StorageService.isOnboardingCompleted();

      // If on splash page, don't redirect
      if (state.matchedLocation == '/splash') {
        return null;
      }

      // If not logged in and trying to access protected routes
      if (!isLoggedIn && _isProtectedRoute(state.matchedLocation)) {
        if (!isOnboardingCompleted) {
          return '/onboarding';
        }
        return '/login';
      }

      // If logged in and trying to access auth routes
      if (isLoggedIn && _isAuthRoute(state.matchedLocation)) {
        return '/home';
      }

      return null;
    },
  );

  static bool _isProtectedRoute(String route) {
    const protectedRoutes = [
      '/home',
      '/matches',
      '/messages',
      '/profile',
      '/chat',
      '/edit-profile',
      '/recharge',
      '/notification-settings',
      '/privacy-settings',
      '/help-support',
    ];
    return protectedRoutes
        .any((protectedRoute) => route.startsWith(protectedRoute));
  }

  static bool _isAuthRoute(String route) {
    const authRoutes = [
      '/login',
      '/register',
      '/onboarding',
    ];
    return authRoutes.contains(route);
  }
}
