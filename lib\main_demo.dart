import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'demo_app.dart';

void main() {
  runApp(const FriendyApp());
}

class FriendyApp extends StatelessWidget {
  const FriendyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Friendy - Dating App',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFE8B4CB),
          brightness: Brightness.light,
        ),
        textTheme: GoogleFonts.poppinsTextTheme(),
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
        ),
      ),
      home: const DemoApp(),
      debugShowCheckedModeBanner: false,
    );
  }
}
