import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors (Soothing, calming tones)
  static const Color primaryPink = Color(0xFFE8B4CB);
  static const Color primaryPurple = Color(0xFFB8A9C9);
  static const Color primaryOrange = Color(0xFFE8C4A0);
  static const Color primaryBlue = Color(0xFFA8C8EC);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryPink, primaryPurple],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryOrange, primaryPink],
  );

  static const LinearGradient blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryPurple],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFFFFF),
      Color(0xFFF8F9FA),
    ],
  );

  // Background Colors
  static const Color backgroundColor = Color(0xFFF8F6F8);
  static const Color backgroundDark = Color(0xFF2A2A2A);
  static const Color surfaceColor = Color(0xFFFFFBFF);
  static const Color cardColor = Color(0xFFFFFBFF);
  static const Color cardDark = Color(0xFF3A3A3A);

  // Text Colors
  static const Color textPrimary = Color(0xFF2D3748);
  static const Color textSecondary = Color(0xFF718096);
  static const Color textLight = Color(0xFFA0AEC0);
  static const Color textWhite = Color(0xFFFFFFFF);

  // Action Colors
  static const Color likeColor = Color(0xFF81C784);
  static const Color passColor = Color(0xFFE57373);
  static const Color superLikeColor = Color(0xFF64B5F6);
  static const Color boostColor = Color(0xFFFFB74D);

  // Status Colors
  static const Color successColor = Color(0xFF66BB6A);
  static const Color errorColor = Color(0xFFEF5350);
  static const Color warningColor = Color(0xFFFFCA28);
  static const Color infoColor = Color(0xFF42A5F5);

  // Border Colors
  static const Color borderColor = Color(0xFFE2E8F0);
  static const Color dividerColor = Color(0xFFEDF2F7);

  // Shadow Colors
  static const Color shadowColor = Color(0x1A000000);
  static const Color cardShadowColor = Color(0x0F000000);

  // Online Status
  static const Color onlineColor = Color(0xFF10B981);
  static const Color offlineColor = Color(0xFF9CA3AF);

  // Message Colors
  static const Color sentMessageColor = Color(0xFFE8B4CB);
  static const Color receivedMessageColor = Color(0xFFF5F3F7);

  // Coin Colors
  static const Color coinGold = Color(0xFFFFD700);
  static const Color coinSilver = Color(0xFFC0C0C0);

  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE2E8F0);
  static const Color shimmerHighlight = Color(0xFFF7FAFC);
}

class AppGradients {
  static const List<Color> pinkPurple = [
    Color(0xFFE8B4CB),
    Color(0xFFB8A9C9),
  ];

  static const List<Color> orangePink = [
    Color(0xFFE8C4A0),
    Color(0xFFE8B4CB),
  ];

  static const List<Color> bluePurple = [
    Color(0xFFA8C8EC),
    Color(0xFFB8A9C9),
  ];

  static const List<Color> sunset = [
    Color(0xFFE8C4A0),
    Color(0xFFF5E6D3),
    Color(0xFFF8F0E8),
  ];

  static const List<Color> ocean = [
    Color(0xFFA8C8EC),
    Color(0xFFB8A9C9),
  ];

  static const List<Color> fire = [
    Color(0xFFE8B4CB),
    Color(0xFFE8C4A0),
  ];
}
